import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, forkJoin } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { Member } from '../../models/member';
import { MemberService } from '../../services/member.service';
import { MemberEntryService } from '../../services/member-entry.service';
import { ToastrService } from 'ngx-toastr';
import { MemberEntry } from '../../models/memberEntry';
import { PaginatedResult } from '../../models/pagination';
import { MemberEntryPagingParameters } from '../../models/memberEntryPagingParameters';

@Component({
  selector: 'app-today-entries',
  templateUrl: './today-entries.component.html',
  styleUrls: ['./today-entries.component.css'],
  standalone: false,
})
export class TodayEntriesComponent implements OnInit {
  entries: MemberEntry[] = [];
  // Will be initialized in constructor
  selectedDate: string;
  isLoading: boolean = false;
  isSearching: boolean = false;
  memberControl = new FormControl();
  members: Member[] = [];
  filteredMembers: Observable<Member[]>;
  selectedMember: Member | null = null;
  isSearched: boolean = false;
  initialDate: string;
  memberSearchDate: string | null = null; // Üye arama modunda seçilen tarih

  // Pagination properties
  paginatedEntries: PaginatedResult<MemberEntry> = {
    data: [],
    pageNumber: 1,
    pageSize: 25,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };
  pageSizeOptions: number[] = [25, 50, 100];
  usePagination: boolean = true;
  Math = Math; // Template'de Math kullanabilmek için

  constructor(
    private memberService: MemberService,
    private memberEntryService: MemberEntryService,
    private toastrService: ToastrService
  ) {
    // Initialize selectedDate with proper formatting to ensure correct date is sent to backend
    this.selectedDate = this.formatDateForBackend(new Date());
    this.initialDate = this.selectedDate;
  }

  ngOnInit(): void {
    this.isLoading = true;
    console.log('Selected date for API:', this.selectedDate);

    // Tüm verileri paralel olarak yükle
    forkJoin({
      members: this.memberService.getMembers()
    }).subscribe({
      next: (response) => {
        this.members = response.members.data;
        console.log('Received members:', this.members.length);

        // Veriler yüklendikten sonra filtreleme ve subscription'ları başlat
        this.initializeFilteredMembers();
        this.setupMemberControlSubscription();

        // Pagination ile günlük girişleri yükle
        this.loadTodayEntriesPaginated();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.toastrService.error('Veriler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  // Format date to ensure correct format for backend
  private formatDateForBackend(date: Date): string {
    // Get local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Format as YYYY-MM-DD
    return `${year}-${month}-${day}`;
  }

  private initializeFilteredMembers() {
    this.filteredMembers = this.memberControl.valueChanges.pipe(
      startWith(''),
      map((value) => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterMembers(name) : this.members.slice();
      })
    );
  }

  private setupMemberControlSubscription() {
    this.memberControl.valueChanges.subscribe((member) => {
      if (member && typeof member !== 'string') {
        this.selectedMember = member;
      }
    });
  }

  getTotalVisitorsToday(): number {
    // Pagination kullanıyorsak toplam sayıyı, değilse mevcut entries'den hesapla
    if (this.usePagination && !this.isSearched) {
      return this.paginatedEntries.totalCount;
    } else {
      const uniqueVisitors = new Set(
        this.entries.map((entry) => entry.phoneNumber)
      );
      return uniqueVisitors.size;
    }
  }

  displayMember(member: Member): string {
    return member ? `${member.name} - ${member.phoneNumber}` : '';
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(
      (member) =>
        member.name.toLowerCase().includes(filterValue) ||
        member.phoneNumber.includes(filterValue)
    );
  }

  onDateChange() {
    // Tarih değiştiğinde anında API isteği atma, sadece "Ara" butonuna basıldığında arama yap
    console.log('Date changed to:', this.selectedDate);
  }

  shouldShowSearchButton(): boolean {
    // Ara butonu şu durumlarda görünür:
    // 1. Üye arama textbox'ında değer varsa
    // 2. Seçilen tarih başlangıç tarihinden farklıysa
    return !!(this.memberControl.value) || (this.selectedDate !== this.initialDate);
  }

  searchMember() {
    // Spam koruması - eğer zaten arama yapılıyorsa çık
    if (this.isSearching) {
      return;
    }

    this.isSearching = true;

    const selectedMember = this.memberControl.value;
    if (selectedMember && (selectedMember.name || selectedMember.phoneNumber)) {
      // İsim filtresi varsa isim bazlı arama yap
      this.isSearched = true;
      this.paginatedEntries.pageNumber = 1; // Arama yaparken ilk sayfaya dön
      this.loadTodayEntriesPaginated();
    } else {
      // İsim filtresi yoksa tarih bazlı arama yap
      this.isSearched = false;
      this.paginatedEntries.pageNumber = 1; // Tarih değiştiğinde ilk sayfaya dön
      this.loadTodayEntriesPaginated();
    }
  }



  clearSearch() {
    this.memberControl.reset();
    this.selectedMember = null;
    this.isSearched = false;
    this.isSearching = false; // Arama durumunu da sıfırla
    this.memberSearchDate = null; // Üye arama tarihini sıfırla
    // Tarihi de başlangıç tarihine sıfırla
    this.selectedDate = this.initialDate;
    // Pagination'ı sıfırla ve yeniden yükle
    this.paginatedEntries.pageNumber = 1;
    this.loadTodayEntriesPaginated();
  }

  // Üye arama modunda tarih filtresi değiştiğinde
  onMemberSearchDateChange(): void {
    if (this.isSearched) {
      this.paginatedEntries.pageNumber = 1; // İlk sayfaya dön
      this.loadTodayEntriesPaginated();
    }
  }

  // Üye arama modunda tarih filtresini temizle
  clearMemberSearchDate(): void {
    this.memberSearchDate = null;
    if (this.isSearched) {
      this.paginatedEntries.pageNumber = 1;
      this.loadTodayEntriesPaginated();
    }
  }

  // Aktif filtrelerin durumunu kontrol et
  hasActiveFilters(): boolean {
    return this.isSearched && this.memberSearchDate !== null;
  }

  // Dinamik başlık için
  getPageTitle(): string {
    if (!this.isSearched) {
      return 'Günlük Giriş/Çıkış Kayıtları';
    }

    if (this.memberSearchDate) {
      const formattedDate = new Date(this.memberSearchDate).toLocaleDateString('tr-TR');
      return `${this.selectedMember?.name} - ${formattedDate} Girişleri`;
    }

    return `${this.selectedMember?.name} - Tüm Giriş Geçmişi`;
  }

  // Pagination metodları
  loadTodayEntriesPaginated() {
    this.isLoading = true;

    const parameters: MemberEntryPagingParameters = {
      pageNumber: this.paginatedEntries.pageNumber,
      pageSize: this.paginatedEntries.pageSize,
      date: this.selectedDate,
      searchText: this.isSearched && this.memberControl.value ?
        (this.memberControl.value.phoneNumber || this.memberControl.value.name) : ''
    };

    console.log('Loading paginated entries with parameters:', parameters);

    if (this.isSearched && parameters.searchText) {
      // Arama modunda yeni paginated API'yi kullan
      const searchParameters: MemberEntryPagingParameters = {
        pageNumber: this.paginatedEntries.pageNumber,
        pageSize: this.paginatedEntries.pageSize,
        searchText: parameters.searchText,
        date: this.memberSearchDate // Üye arama modunda seçilen tarih (null ise tüm geçmiş)
      };

      this.memberEntryService.getMemberEntriesBySearchPaginated(searchParameters).subscribe({
        next: (response) => {
          this.paginatedEntries = response.data;
          this.entries = response.data.data; // Backward compatibility için
          console.log('Received member search paginated entries:', this.paginatedEntries);
          this.isLoading = false;
          setTimeout(() => {
            this.isSearching = false;
          }, 1000);
        },
        error: (error) => {
          console.error('Error fetching member entries:', error);
          this.toastrService.error('Üye girişleri yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
          setTimeout(() => {
            this.isSearching = false;
          }, 1000);
        }
      });
    } else {
      // Normal pagination modunda yeni API'yi kullan
      this.memberEntryService.getTodayEntriesPaginated(parameters).subscribe({
        next: (response) => {
          this.paginatedEntries = response.data;
          this.entries = response.data.data; // Backward compatibility için
          console.log('Received paginated entries:', this.paginatedEntries);
          this.isLoading = false;
          setTimeout(() => {
            this.isSearching = false;
          }, 1000);
        },
        error: (error) => {
          console.error('Error fetching paginated entries:', error);
          this.toastrService.error('Günlük girişler yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
          setTimeout(() => {
            this.isSearching = false;
          }, 1000);
        }
      });
    }
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.paginatedEntries.totalPages && page !== this.paginatedEntries.pageNumber) {
      this.paginatedEntries.pageNumber = page;
      this.loadTodayEntriesPaginated();
    }
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newPageSize = parseInt(target.value);
    this.paginatedEntries.pageSize = newPageSize;
    this.paginatedEntries.pageNumber = 1; // Reset to first page
    this.loadTodayEntriesPaginated();
  }

  getPaginationRange(): number[] {
    const totalPages = this.paginatedEntries.totalPages;
    const currentPage = this.paginatedEntries.pageNumber;
    const range: number[] = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  getMembers() {
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
      },
      error: (error) => {
        console.error('Error fetching members:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
      },
    });
  }

  calculateDurationInMinutes(entryTime: Date, exitTime: Date): number {
    return Math.floor(
      (new Date(exitTime).getTime() - new Date(entryTime).getTime()) /
        (1000 * 60)
    );
  }

  calculateDuration(entryTime: Date, exitTime: Date | null): string {
    if (!exitTime) {
      return '-';
    }

    const duration = this.calculateDurationInMinutes(entryTime, exitTime);
    if (duration >= 301) {
      return '-';
    }

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    return hours > 0 ? `${hours} saat ${minutes} dakika` : `${minutes} dakika`;
  }

  isActiveEntry(entry: MemberEntry): boolean {
    return !entry.exitTime;
  }

  shouldShowQRWarning(entry: MemberEntry): boolean {
    if (!entry.exitTime) return false;
    const duration = this.calculateDurationInMinutes(
      entry.entryTime,
      entry.exitTime
    );
    return duration >= 301;
  }
  
  // Get initials from name
  getInitials(name: string): string {
    if (!name) return '';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
  
  // Generate avatar background color based on name
  getAvatarColor(name: string): string {
    if (!name) return '#4361ee';
    
    const colors = [
      '#4361ee', '#3a0ca3', '#7209b7', '#f72585', 
      '#4cc9f0', '#4895ef', '#560bad', '#b5179e',
      '#3f37c9', '#4361ee', '#4cc9f0', '#00b4d8'
    ];
    
    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
  
  // Get duration class based on duration
  getDurationClass(entry: MemberEntry): string {
    if (!entry.exitTime) return '';
    
    const duration = this.calculateDurationInMinutes(entry.entryTime, entry.exitTime);
    
    if (duration < 30) return 'danger';
    if (duration < 60) return 'warning';
    return 'success';
  }
  
}